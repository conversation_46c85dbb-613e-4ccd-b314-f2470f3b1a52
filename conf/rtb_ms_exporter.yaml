Server:
  Name: rtb_ms_exporter
  Port: 8080

LogConfig:
  LogPath: log/rtb_ms_exporter.log
  LogLevel: info
  MaxAgeDays: 7
  MaxSize: 100
  MaxBackups: 10
  Compress: true

# 数据库配置
databases:
  rtb:
    host: dbm.office.domob-inc.cn
    port: 3306
    user: domob
    password: domob
    dbname: rtb
    charset: utf8mb4
  device:
    host: dbm.office.domob-inc.cn
    port: 3306
    user: domob
    password: domob
    dbname: rtb
    charset: utf8mb4
  bid:
    host: dbm.office.domob-inc.cn
    port: 3306
    user: domob
    password: domob
    dbname: rtb
    charset: utf8mb4

# 对象存储配置
oss_providers:
  bos:
    type: bos
    endpoint: https://bj.bcebos.com
    access_key_id: your_bos_access_key
    access_key_secret: your_bos_secret_key
    bucket_name: your-bos-bucket
    region: bj
    cleanup_days: 1
  cos:
    type: cos
    endpoint: https://your-bucket-**********.cos.ap-beijing.myqcloud.com
    access_key_id: your_cos_secret_id
    access_key_secret: your_cos_secret_key
    bucket_name: your-cos-bucket
    region: ap-beijing
    cleanup_days: 1

# AdIndex文件监听配置
adindex:
  watch_dir: /Users/<USER>/codes/rtb_ms_exporter_go/source
  file_pattern: '^ad_\w+\.data\.\d{8}_\d{6}$'
  keep_files: 3  # 每种类型保留的文件数量

# 定时任务配置
schedule:
  budget_interval: "10s"    # budget任务每10秒执行一次
  device_interval: "10s"    # device任务每24小时执行一次
  resource_interval: "10s"  # resource任务每10分钟执行一次
  budget_keep_files: 5      # budget文件保留数量
  resource_keep_files: 5    # resource文件保留数量

# 本地存储配置
local_storage:
  base_dir: /Users/<USER>/codes/rtb_ms_exporter_go/backup
  cleanup_days: 3  # 本地文件保留3天