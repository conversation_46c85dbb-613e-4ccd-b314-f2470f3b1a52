package conf

import (
	"github.com/spf13/viper"
)

type (
	ServerConfig struct {
		Name string `yaml:"Name" mapstructure:"Name"`
		Port int    `yaml:"Port" mapstructure:"Port"`
	}
	LogConfig struct {
		LogLevel   string `yaml:"LogLevel" mapstructure:"LogLevel"`
		LogPath    string `yaml:"LogPath" mapstructure:"LogPath"`
		MaxAgeDays int    `yaml:"MaxAgeDays" mapstructure:"MaxAgeDays"`
		MaxSize    int    `yaml:"MaxSize" mapstructure:"MaxSize"`
		MaxBackups int    `yaml:"MaxBackups" mapstructure:"MaxBackups"`
		Compress   bool   `yaml:"Compress" mapstructure:"Compress"`
	}

	// 数据库配置
	DatabaseConfig struct {
		Host     string `yaml:"host" mapstructure:"host"`
		Port     int    `yaml:"port" mapstructure:"port"`
		User     string `yaml:"user" mapstructure:"user"`
		Password string `yaml:"password" mapstructure:"password"`
		DBName   string `yaml:"dbname" mapstructure:"dbname"`
		Charset  string `yaml:"charset" mapstructure:"charset"`
	}

	// 对象存储配置
	OSSConfig struct {
		Type            string `yaml:"type" mapstructure:"type"` // bos, cos, oss等
		Endpoint        string `yaml:"endpoint" mapstructure:"endpoint"`
		AccessKeyID     string `yaml:"access_key_id" mapstructure:"access_key_id"`
		AccessKeySecret string `yaml:"access_key_secret" mapstructure:"access_key_secret"`
		BucketName      string `yaml:"bucket_name" mapstructure:"bucket_name"`
		Region          string `yaml:"region" mapstructure:"region"`
		CleanupDays     int    `yaml:"cleanup_days" mapstructure:"cleanup_days"` // 清理超过N天的文件
	}

	// AdIndex配置
	AdIndexConfig struct {
		WatchDir    string `yaml:"watch_dir" mapstructure:"watch_dir"`       // 监听目录
		FilePattern string `yaml:"file_pattern" mapstructure:"file_pattern"` // 文件匹配模式
		KeepFiles   int    `yaml:"keep_files" mapstructure:"keep_files"`     // 每种类型保留的文件数量，默认3个
	}

	// 定时任务配置
	ScheduleConfig struct {
		BudgetInterval    string `yaml:"budget_interval" mapstructure:"budget_interval"`         // budget任务间隔，如"10s"
		DeviceInterval    string `yaml:"device_interval" mapstructure:"device_interval"`         // device任务间隔，如"24h"
		ResourceInterval  string `yaml:"resource_interval" mapstructure:"resource_interval"`     // resource任务间隔，如"10m"
		BudgetKeepFiles   int    `yaml:"budget_keep_files" mapstructure:"budget_keep_files"`     // budget文件保留数量
		ResourceKeepFiles int    `yaml:"resource_keep_files" mapstructure:"resource_keep_files"` // resource文件保留数量
	}

	// 本地存储配置
	LocalStorageConfig struct {
		BaseDir     string `yaml:"base_dir" mapstructure:"base_dir"`         // 本地存储基础目录
		CleanupDays int    `yaml:"cleanup_days" mapstructure:"cleanup_days"` // 本地文件清理天数
	}

	// 监控配置
	MonitorConfig struct {
		Enabled        bool   `yaml:"enabled" mapstructure:"enabled"`                 // 是否启用监控
		HTTPPort       int    `yaml:"http_port" mapstructure:"http_port"`             // HTTP监控端口
		ReportInterval string `yaml:"report_interval" mapstructure:"report_interval"` // 报告间隔
	}
)

type Config struct {
	Server       ServerConfig              `yaml:"Server" mapstructure:"Server"`
	LogConfig    LogConfig                 `yaml:"LogConfig" mapstructure:"LogConfig"`
	Databases    map[string]DatabaseConfig `yaml:"databases" mapstructure:"databases"`         // 支持多个数据库
	OSSProviders map[string]OSSConfig      `yaml:"oss_providers" mapstructure:"oss_providers"` // 支持多个对象存储
	AdIndex      AdIndexConfig             `yaml:"adindex" mapstructure:"adindex"`
	Schedule     ScheduleConfig            `yaml:"schedule" mapstructure:"schedule"`
	LocalStorage LocalStorageConfig        `yaml:"local_storage" mapstructure:"local_storage"`
	Monitor      MonitorConfig             `yaml:"monitor" mapstructure:"monitor"`           // 监控配置
	DebugOSS     bool                      // OSS调试模式，只打印不上传
}

var GlobalConfig Config

func ConfigInit(configPath string) (err error) {
	viper.SetConfigType("yaml")
	viper.SetConfigFile(configPath)

	if err = viper.ReadInConfig(); err != nil {
		return err
	}
	if err = viper.Unmarshal(&GlobalConfig); err != nil {
		return err
	}
	return nil
}
