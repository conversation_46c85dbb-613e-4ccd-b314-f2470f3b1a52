package scheduler

import (
	"fmt"
	"os"
	"path/filepath"
	"rtb_ms_exporter_go/conf"
	"rtb_ms_exporter_go/internal/models"
	"rtb_ms_exporter_go/internal/storage"
	"rtb_ms_exporter_go/internal/watcher"
	"sort"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// TaskScheduler 任务调度器
type TaskScheduler struct {
	cron           *cron.Cron
	dbManager      *models.DatabaseManager
	storageManager *storage.StorageManager
	localManager   *watcher.LocalFileManager
	config         conf.ScheduleConfig
	logger         *zap.Logger
}

// NewTaskScheduler 创建任务调度器
func NewTaskScheduler(
	dbManager *models.DatabaseManager,
	storageManager *storage.StorageManager,
	localManager *watcher.LocalFileManager,
	config conf.ScheduleConfig,
	logger *zap.Logger,
) *TaskScheduler {
	return &TaskScheduler{
		cron:           cron.New(cron.WithSeconds()),
		dbManager:      dbManager,
		storageManager: storageManager,
		localManager:   localManager,
		config:         config,
		logger:         logger,
	}
}

// Start 启动调度器
func (ts *TaskScheduler) Start() error {
	// 添加budget任务
	if err := ts.addBudgetTask(); err != nil {
		return fmt.Errorf("failed to add budget task: %v", err)
	}

	// 添加device任务
	if err := ts.addDeviceTask(); err != nil {
		return fmt.Errorf("failed to add device task: %v", err)
	}

	// 添加resource任务
	if err := ts.addResourceTask(); err != nil {
		return fmt.Errorf("failed to add resource task: %v", err)
	}

	// 启动调度器
	ts.cron.Start()
	ts.logger.Info("Task scheduler started")

	return nil
}

// addBudgetTask 添加budget任务
func (ts *TaskScheduler) addBudgetTask() error {
	cronExpr, err := ts.intervalToCron(ts.config.BudgetInterval)
	if err != nil {
		return err
	}

	_, err = ts.cron.AddFunc(cronExpr, func() {
		ts.logger.Info("Starting budget task")
		if err := ts.executeBudgetTask(); err != nil {
			ts.logger.Error("Budget task failed", zap.Error(err))
		} else {
			ts.logger.Info("Budget task completed")
		}
	})

	if err != nil {
		return err
	}

	ts.logger.Info("Budget task scheduled", zap.String("interval", ts.config.BudgetInterval))
	return nil
}

// addDeviceTask 添加device任务
func (ts *TaskScheduler) addDeviceTask() error {
	cronExpr, err := ts.intervalToCron(ts.config.DeviceInterval)
	if err != nil {
		return err
	}

	_, err = ts.cron.AddFunc(cronExpr, func() {
		ts.logger.Info("Starting device task")
		if err := ts.executeDeviceTask(); err != nil {
			ts.logger.Error("Device task failed", zap.Error(err))
		} else {
			ts.logger.Info("Device task completed")
		}
	})

	if err != nil {
		return err
	}

	ts.logger.Info("Device task scheduled", zap.String("interval", ts.config.DeviceInterval))
	return nil
}

// addResourceTask 添加resource任务
func (ts *TaskScheduler) addResourceTask() error {
	cronExpr, err := ts.intervalToCron(ts.config.ResourceInterval)
	if err != nil {
		return err
	}

	_, err = ts.cron.AddFunc(cronExpr, func() {
		ts.logger.Info("Starting resource task")
		if err := ts.executeResourceTask(); err != nil {
			ts.logger.Error("Resource task failed", zap.Error(err))
		} else {
			ts.logger.Info("Resource task completed")
		}
	})

	if err != nil {
		return err
	}

	ts.logger.Info("Resource task scheduled", zap.String("interval", ts.config.ResourceInterval))
	return nil
}

// intervalToCron 将间隔时间转换为cron表达式
func (ts *TaskScheduler) intervalToCron(interval string) (string, error) {
	duration, err := time.ParseDuration(interval)
	if err != nil {
		return "", err
	}

	// 根据时间间隔生成cron表达式
	switch {
	case duration < time.Minute:
		// 秒级间隔
		seconds := int(duration.Seconds())
		return fmt.Sprintf("*/%d * * * * *", seconds), nil
	case duration < time.Hour:
		// 分钟级间隔
		minutes := int(duration.Minutes())
		return fmt.Sprintf("0 */%d * * * *", minutes), nil
	case duration < 24*time.Hour:
		// 小时级间隔
		hours := int(duration.Hours())
		return fmt.Sprintf("0 0 */%d * * *", hours), nil
	default:
		// 天级间隔
		days := int(duration.Hours() / 24)
		if days == 1 {
			return "0 0 0 * * *", nil // 每天执行一次
		}
		return fmt.Sprintf("0 0 0 */%d * *", days), nil
	}
}

// executeBudgetTask 执行budget任务
func (ts *TaskScheduler) executeBudgetTask() error {
	startTime := time.Now()
	// 获取当前日期和时间戳
	today := time.Now().Format("20060102")
	timestamp := time.Now().Format("20060102150405")
	ts.logger.Info("Executing budget task", zap.String("date", today))

	// 查询数据
	stats, err := ts.dbManager.QueryBudgetStats(today)
	if err != nil {
		return fmt.Errorf("failed to query budget stats: %v", err)
	}

	// 记录查询结果
	ts.logger.Info("Budget stats query completed",
		zap.Int("record_count", len(stats)),
		zap.String("date", today))

	// 格式化数据
	content := models.FormatBudgetStats(stats)
	contentReader := strings.NewReader(content)

	// 记录内容长度
	ts.logger.Info("Budget content formatted",
		zap.Int("content_length", len(content)),
		zap.Int("record_count", len(stats)))

	// 生成带时间戳的文件名
	fileName := fmt.Sprintf("budget/stats.data.%s", timestamp)

	// 保存到本地
	if err := ts.localManager.SaveFile(fileName, strings.NewReader(content)); err != nil {
		ts.logger.Error("Failed to save budget file locally", zap.Error(err))
	}

	// 上传到对象存储
	err = ts.storageManager.UploadToAll(fileName, contentReader, "text/plain")
	
	// 执行自动清理
	if err == nil {
		ts.cleanupBudgetFiles()
	}
	
	duration := time.Since(startTime)
	if err != nil {
		ts.logger.Error("Budget task failed", 
			zap.String("date", today),
			zap.Duration("total_duration", duration),
			zap.Error(err))
	} else {
		ts.logger.Info("Budget task completed successfully", 
			zap.String("date", today),
			zap.Int("record_count", len(stats)),
			zap.Int("content_length", len(content)),
			zap.Duration("total_duration", duration))
	}
	
	return err
}

// executeDeviceTask 执行device任务
func (ts *TaskScheduler) executeDeviceTask() error {
	startTime := time.Now()
	ts.logger.Info("Executing device task")
	
	// 查询数据
	mappings, err := ts.dbManager.QueryDeviceMapping()
	if err != nil {
		return fmt.Errorf("failed to query device mapping: %v", err)
	}

	// 格式化数据
	content := models.FormatDeviceMapping(mappings)
	contentReader := strings.NewReader(content)

	// 记录内容长度
	ts.logger.Info("Device content formatted",
		zap.Int("content_length", len(content)),
		zap.Int("record_count", len(mappings)))

	// 使用固定文件名，不带时间戳
	fileName := "device/device_mapping.txt"

	// 保存到本地
	if err := ts.localManager.SaveFile(fileName, strings.NewReader(content)); err != nil {
		ts.logger.Error("Failed to save device file locally", zap.Error(err))
	}

	// 上传到对象存储
	err = ts.storageManager.UploadToAll(fileName, contentReader, "text/plain")
	
	duration := time.Since(startTime)
	if err != nil {
		ts.logger.Error("Device task failed", 
			zap.Duration("total_duration", duration),
			zap.Error(err))
	} else {
		ts.logger.Info("Device task completed successfully", 
			zap.Int("record_count", len(mappings)),
			zap.Int("content_length", len(content)),
			zap.Duration("total_duration", duration))
	}
	
	return err
}

// executeResourceTask 执行resource任务
func (ts *TaskScheduler) executeResourceTask() error {
	startTime := time.Now()
	timestamp := time.Now().Format("20060102150405")
	ts.logger.Info("Executing resource task")
	
	// 查询数据
	targets, err := ts.dbManager.QueryResourceTarget()
	if err != nil {
		return fmt.Errorf("failed to query resource target: %v", err)
	}

	// 格式化数据
	content := models.FormatResourceTarget(targets)
	contentReader := strings.NewReader(content)

	// 记录内容长度
	ts.logger.Info("Resource content formatted",
		zap.Int("content_length", len(content)),
		zap.Int("record_count", len(targets)))

	// 生成带时间戳的文件名
	fileName := fmt.Sprintf("resource/resource-target.data.%s", timestamp)

	// 保存到本地
	if err := ts.localManager.SaveFile(fileName, strings.NewReader(content)); err != nil {
		ts.logger.Error("Failed to save resource file locally", zap.Error(err))
	}

	// 上传到对象存储
	err = ts.storageManager.UploadToAll(fileName, contentReader, "text/plain")
	
	// 执行自动清理
	if err == nil {
		ts.cleanupResourceFiles()
	}
	
	duration := time.Since(startTime)
	if err != nil {
		ts.logger.Error("Resource task failed", 
			zap.Duration("total_duration", duration),
			zap.Error(err))
	} else {
		ts.logger.Info("Resource task completed successfully", 
			zap.Int("record_count", len(targets)),
			zap.Int("content_length", len(content)),
			zap.Duration("total_duration", duration))
	}
	
	return err
}

// ExecuteAllTasksOnce 立即执行所有任务一次
func (ts *TaskScheduler) ExecuteAllTasksOnce() error {
	ts.logger.Info("Executing all tasks once on startup")

	// 执行budget任务
	ts.logger.Info("Executing budget task on startup")
	if err := ts.executeBudgetTask(); err != nil {
		ts.logger.Error("Budget task failed on startup", zap.Error(err))
		return fmt.Errorf("budget task failed: %v", err)
	}

	// 执行device任务
	ts.logger.Info("Executing device task on startup")
	if err := ts.executeDeviceTask(); err != nil {
		ts.logger.Error("Device task failed on startup", zap.Error(err))
		return fmt.Errorf("device task failed: %v", err)
	}

	// 执行resource任务
	ts.logger.Info("Executing resource task on startup")
	if err := ts.executeResourceTask(); err != nil {
		ts.logger.Error("Resource task failed on startup", zap.Error(err))
		return fmt.Errorf("resource task failed: %v", err)
	}

	ts.logger.Info("All tasks executed successfully on startup")
	return nil
}

// Stop 停止调度器
func (ts *TaskScheduler) Stop() {
	ts.logger.Info("Stopping task scheduler")
	ctx := ts.cron.Stop()
	<-ctx.Done()
	ts.logger.Info("Task scheduler stopped")
}

// CleanupManager 清理管理器
type CleanupManager struct {
	storageManager *storage.StorageManager
	localManager   *watcher.LocalFileManager
	logger         *zap.Logger
}

// NewCleanupManager 创建清理管理器
func NewCleanupManager(
	storageManager *storage.StorageManager,
	localManager *watcher.LocalFileManager,
	logger *zap.Logger,
) *CleanupManager {
	return &CleanupManager{
		storageManager: storageManager,
		localManager:   localManager,
		logger:         logger,
	}
}

// StartCleanupSchedule 启动清理调度
func (cm *CleanupManager) StartCleanupSchedule(ossCleanupDays, localCleanupDays int) {
	// 创建清理调度器，使用标准5字段格式
	cleanupCron := cron.New()

	// 每天凌晨2点执行清理任务 (分 时 日 月 周)
	_, err := cleanupCron.AddFunc("0 2 * * *", func() {
		cm.logger.Info("Starting cleanup task")

		// 清理对象存储中的adindex旧文件
		if err := cm.storageManager.CleanupAllProviders("adindex/", ossCleanupDays); err != nil {
			cm.logger.Error("Failed to cleanup OSS adindex files", zap.Error(err))
		}

		// 清理本地文件
		if err := cm.localManager.CleanupOldFiles(localCleanupDays); err != nil {
			cm.logger.Error("Failed to cleanup local files", zap.Error(err))
		}

		cm.logger.Info("Cleanup task completed")
	})

	if err != nil {
		cm.logger.Error("Failed to schedule cleanup task", zap.Error(err))
		return
	}

	cleanupCron.Start()
	cm.logger.Info("Cleanup scheduler started")
}

// cleanupBudgetFiles 清理budget文件
func (ts *TaskScheduler) cleanupBudgetFiles() {
	keepFiles := ts.config.BudgetKeepFiles
	if keepFiles <= 0 {
		keepFiles = 3 // 默认保留3个文件
	}
	ts.cleanupFilesByPattern("budget", "stats.data.", keepFiles)
}



// cleanupResourceFiles 清理resource文件
func (ts *TaskScheduler) cleanupResourceFiles() {
	keepFiles := ts.config.ResourceKeepFiles
	if keepFiles <= 0 {
		keepFiles = 3 // 默认保留3个文件
	}
	ts.cleanupFilesByPattern("resource", "resource-target.data.", keepFiles)
}

// cleanupFilesByPattern 按模式清理文件
func (ts *TaskScheduler) cleanupFilesByPattern(directory, filePrefix string, keepFiles int) {
	ts.logger.Info("Starting cleanup", 
		zap.String("directory", directory),
		zap.String("prefix", filePrefix),
		zap.Int("keep_files", keepFiles))

	// 清理本地文件
	ts.cleanupLocalFilesByPattern(directory, filePrefix, keepFiles)

	// 清理远程文件
	ts.cleanupRemoteFilesByPattern(directory, filePrefix, keepFiles)
}

// cleanupLocalFilesByPattern 清理本地文件
func (ts *TaskScheduler) cleanupLocalFilesByPattern(directory, filePrefix string, keepFiles int) {
	localDir := filepath.Join(ts.localManager.BaseDir(), directory)
	if _, err := os.Stat(localDir); os.IsNotExist(err) {
		return
	}

	files, err := filepath.Glob(filepath.Join(localDir, filePrefix+"*"))
	if err != nil {
		ts.logger.Error("Failed to list local files", zap.Error(err))
		return
	}

	// 按文件名排序（时间戳排序）
	sort.Strings(files)

	// 删除多余的文件
	if len(files) > keepFiles {
		filesToDelete := files[:len(files)-keepFiles]
		for _, file := range filesToDelete {
			if err := os.Remove(file); err != nil {
				ts.logger.Error("Failed to remove local file", 
					zap.String("file", file), zap.Error(err))
			} else {
				ts.logger.Info("Removed old local file", 
					zap.String("file", filepath.Base(file)))
			}
		}
	}
}

// cleanupRemoteFilesByPattern 清理远程文件
func (ts *TaskScheduler) cleanupRemoteFilesByPattern(directory, filePrefix string, keepFiles int) {
	// 获取所有存储提供商
	providers := ts.storageManager.GetProviders()
	
	for providerName, provider := range providers {
		ts.logger.Info("Cleaning up remote files", 
			zap.String("provider", providerName),
			zap.String("directory", directory))

		// 列出远程文件
		remoteFiles, err := provider.ListFiles(directory + "/")
		if err != nil {
			ts.logger.Error("Failed to list remote files", 
				zap.String("provider", providerName), zap.Error(err))
			continue
		}

		// 过滤匹配的文件
		var matchedFiles []storage.FileInfo
		for _, file := range remoteFiles {
			if strings.HasPrefix(filepath.Base(file.Key), filePrefix) {
				matchedFiles = append(matchedFiles, file)
			}
		}

		// 按修改时间排序
		sort.Slice(matchedFiles, func(i, j int) bool {
			return matchedFiles[i].LastModified.Before(matchedFiles[j].LastModified)
		})

		// 删除多余的文件
		if len(matchedFiles) > keepFiles {
			filesToDelete := matchedFiles[:len(matchedFiles)-keepFiles]
			for _, file := range filesToDelete {
				if err := provider.DeleteFile(file.Key); err != nil {
					ts.logger.Error("Failed to remove remote file", 
						zap.String("provider", providerName),
						zap.String("file", file.Key), zap.Error(err))
				} else {
					ts.logger.Info("Removed old remote file", 
						zap.String("provider", providerName),
						zap.String("file", file.Key))
				}
			}
		}
	}
}
