package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
)

// MonitorManager 监控管理器
type MonitorManager struct {
	healthMonitor     *HealthMonitor
	errorMonitor      *ErrorMonitor
	logRotationMonitor *LogRotationMonitor
	config            Config
	logger            *zap.Logger
	httpServer        *http.Server
	mu                sync.RWMutex
	startTime         time.Time
}

// Config 监控配置
type Config struct {
	Enabled      bool   `yaml:"enabled" mapstructure:"enabled"`           // 是否启用监控
	HTTPPort     int    `yaml:"http_port" mapstructure:"http_port"`       // HTTP监控端口
	ReportInterval string `yaml:"report_interval" mapstructure:"report_interval"` // 报告间隔
	LogPath      string `yaml:"log_path" mapstructure:"log_path"`         // 日志文件路径
}

// MonitorData 监控数据
type MonitorData struct {
	Timestamp    time.Time            `json:"timestamp"`
	Health       HealthData           `json:"health"`
	Errors       ErrorData            `json:"errors"`
	LogRotation  LogRotationData      `json:"log_rotation"`
}

// NewMonitorManager 创建监控管理器
func NewMonitorManager(config Config, logPath string, logger *zap.Logger) *MonitorManager {
	return &MonitorManager{
		healthMonitor:      NewHealthMonitor(logger),
		errorMonitor:       NewErrorMonitor(logger),
		logRotationMonitor: NewLogRotationMonitor(logPath, logger),
		config:            config,
		logger:            logger,
		startTime:         time.Now(),
	}
}

// Start 启动监控服务
func (mm *MonitorManager) Start() error {
	if !mm.config.Enabled {
		mm.logger.Info("Monitor is disabled")
		return nil
	}

	// 启动各个监控组件
	if err := mm.healthMonitor.Start(); err != nil {
		return fmt.Errorf("failed to start health monitor: %v", err)
	}

	if err := mm.errorMonitor.Start(); err != nil {
		return fmt.Errorf("failed to start error monitor: %v", err)
	}

	if err := mm.logRotationMonitor.Start(); err != nil {
		return fmt.Errorf("failed to start log rotation monitor: %v", err)
	}

	// 启动HTTP服务
	if err := mm.startHTTPServer(); err != nil {
		return fmt.Errorf("failed to start HTTP server: %v", err)
	}

	// 启动定期报告
	go mm.startPeriodicReport()

	mm.logger.Info("Monitor manager started", 
		zap.Int("http_port", mm.config.HTTPPort),
		zap.String("report_interval", mm.config.ReportInterval))

	return nil
}

// Stop 停止监控服务
func (mm *MonitorManager) Stop() {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	// 停止HTTP服务
	if mm.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := mm.httpServer.Shutdown(ctx); err != nil {
			mm.logger.Error("Failed to shutdown HTTP server", zap.Error(err))
		}
	}

	// 停止各个监控组件
	mm.healthMonitor.Stop()
	mm.errorMonitor.Stop()
	mm.logRotationMonitor.Stop()

	mm.logger.Info("Monitor manager stopped")
}

// GetErrorMonitor 获取错误监控器
func (mm *MonitorManager) GetErrorMonitor() *ErrorMonitor {
	return mm.errorMonitor
}

// startHTTPServer 启动HTTP服务
func (mm *MonitorManager) startHTTPServer() error {
	mux := http.NewServeMux()
	
	// 健康检查接口
	mux.HandleFunc("/health", mm.handleHealth)
	
	// 监控指标接口
	mux.HandleFunc("/metrics", mm.handleMetrics)
	
	// 详细监控数据接口
	mux.HandleFunc("/monitor", mm.handleMonitor)

	mm.httpServer = &http.Server{
		Addr:    fmt.Sprintf(":%d", mm.config.HTTPPort),
		Handler: mux,
	}

	go func() {
		if err := mm.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			mm.logger.Error("HTTP server error", zap.Error(err))
		}
	}()

	return nil
}

// handleHealth 处理健康检查请求
func (mm *MonitorManager) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	health := mm.healthMonitor.GetHealthData()
	
	// 简单的健康状态判断
	status := "healthy"
	if health.MemoryUsageMB > 1000 || health.GoroutineCount > 1000 {
		status = "warning"
	}
	
	response := map[string]interface{}{
		"status":    status,
		"timestamp": time.Now(),
		"uptime":    time.Since(mm.startTime).String(),
		"health":    health,
	}
	
	json.NewEncoder(w).Encode(response)
}

// handleMetrics 处理监控指标请求
func (mm *MonitorManager) handleMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	data := mm.collectMonitorData()
	json.NewEncoder(w).Encode(data)
}

// handleMonitor 处理详细监控数据请求
func (mm *MonitorManager) handleMonitor(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	data := mm.collectMonitorData()
	
	// 添加更多详细信息
	response := map[string]interface{}{
		"monitor_data": data,
		"uptime":      time.Since(mm.startTime).String(),
		"start_time":  mm.startTime,
		"config":      mm.config,
	}
	
	json.NewEncoder(w).Encode(response)
}

// collectMonitorData 收集监控数据
func (mm *MonitorManager) collectMonitorData() MonitorData {
	return MonitorData{
		Timestamp:   time.Now(),
		Health:      mm.healthMonitor.GetHealthData(),
		Errors:      mm.errorMonitor.GetErrorData(),
		LogRotation: mm.logRotationMonitor.GetLogRotationData(),
	}
}

// startPeriodicReport 启动定期报告
func (mm *MonitorManager) startPeriodicReport() {
	interval, err := time.ParseDuration(mm.config.ReportInterval)
	if err != nil {
		mm.logger.Error("Invalid report interval", zap.String("interval", mm.config.ReportInterval))
		interval = 1 * time.Hour // 默认1小时
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mm.generateReport()
		}
	}
}

// generateReport 生成监控报告
func (mm *MonitorManager) generateReport() {
	data := mm.collectMonitorData()
	
	mm.logger.Info("Monitor Report",
		zap.Time("timestamp", data.Timestamp),
		zap.Float64("memory_mb", data.Health.MemoryUsageMB),
		zap.Int("goroutines", data.Health.GoroutineCount),
		zap.Int64("total_errors", data.Errors.TotalErrors),
		zap.Float64("error_rate", data.Errors.ErrorRate),
		zap.Int64("log_size_mb", data.LogRotation.CurrentLogSizeMB),
		zap.Int("backup_count", data.LogRotation.BackupCount),
	)
}
