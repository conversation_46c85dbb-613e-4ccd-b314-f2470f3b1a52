package monitor

import (
	"sync"
	"time"

	"go.uber.org/zap"
)

// MonitorManager 监控管理器
type MonitorManager struct {
	healthMonitor      *HealthMonitor
	errorMonitor       *ErrorMonitor
	logRotationMonitor *LogRotationMonitor
	config             Config
	logger             *zap.Logger
	mu                 sync.RWMutex
	startTime          time.Time
	stopCh             chan struct{}
}

// Config 监控配置
type Config struct {
	Enabled        bool   `yaml:"enabled" mapstructure:"enabled"`                 // 是否启用监控
	ReportInterval string `yaml:"report_interval" mapstructure:"report_interval"` // 报告间隔
}

// NewMonitorManager 创建监控管理器
func NewMonitorManager(config Config, logPath string, logger *zap.Logger) *MonitorManager {
	return &MonitorManager{
		healthMonitor:      NewHealthMonitor(logger),
		errorMonitor:       NewErrorMonitor(logger),
		logRotationMonitor: NewLogRotationMonitor(logPath, logger),
		config:             config,
		logger:             logger,
		startTime:          time.Now(),
		stopCh:             make(chan struct{}),
	}
}

// Start 启动监控服务
func (mm *MonitorManager) Start() error {
	if !mm.config.Enabled {
		mm.logger.Info("Monitor disabled")
		return nil
	}

	// 启动各个监控组件
	if err := mm.healthMonitor.Start(); err != nil {
		return err
	}

	if err := mm.errorMonitor.Start(); err != nil {
		return err
	}

	if err := mm.logRotationMonitor.Start(); err != nil {
		return err
	}

	// 启动定期报告
	go mm.startPeriodicReport()

	mm.logger.Info("Monitor started", 
		zap.String("report_interval", mm.config.ReportInterval))

	return nil
}

// Stop 停止监控服务
func (mm *MonitorManager) Stop() {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	// 发送停止信号
	close(mm.stopCh)

	// 停止各个监控组件
	mm.healthMonitor.Stop()
	mm.errorMonitor.Stop()
	mm.logRotationMonitor.Stop()

	mm.logger.Info("Monitor stopped")
}

// GetErrorMonitor 获取错误监控器
func (mm *MonitorManager) GetErrorMonitor() *ErrorMonitor {
	return mm.errorMonitor
}

// startPeriodicReport 启动定期报告
func (mm *MonitorManager) startPeriodicReport() {
	interval, err := time.ParseDuration(mm.config.ReportInterval)
	if err != nil {
		mm.logger.Error("Invalid report interval", 
			zap.String("interval", mm.config.ReportInterval), 
			zap.Error(err))
		interval = 1 * time.Hour // 默认1小时
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mm.generateReport()
		case <-mm.stopCh:
			return
		}
	}
}

// generateReport 生成监控报告
func (mm *MonitorManager) generateReport() {
	// 收集监控数据
	healthData := mm.healthMonitor.GetHealthData()
	errorData := mm.errorMonitor.GetErrorData()
	logRotationData := mm.logRotationMonitor.GetLogRotationData()

	// 计算运行时间
	uptime := time.Since(mm.startTime)

	// 输出监控摘要，模仿现有模块的日志格式
	mm.logger.Info("Monitor report",
		zap.String("uptime", uptime.String()),
		zap.Float64("memory_mb", healthData.MemoryUsageMB),
		zap.Int("goroutines", healthData.GoroutineCount),
		zap.Uint32("gc_count", healthData.GCCount),
		zap.Int64("total_errors", errorData.TotalErrors),
		zap.Float64("error_rate_per_hour", errorData.ErrorRate),
		zap.Int64("log_size_mb", logRotationData.CurrentLogSizeMB),
		zap.Int("log_backups", logRotationData.BackupCount),
		zap.Float64("disk_usage_percent", logRotationData.DiskUsagePercent),
		zap.Bool("rotation_healthy", logRotationData.IsRotationHealthy),
	)

	// 如果有错误，输出错误统计详情
	if errorData.TotalErrors > 0 {
		mm.logger.Info("Error statistics",
			zap.Int64("total_errors", errorData.TotalErrors),
			zap.Float64("error_rate", errorData.ErrorRate),
			zap.Time("last_error", errorData.LastErrorTime),
		)

		// 输出各模块错误统计
		for moduleName, stats := range errorData.ModuleStats {
			mm.logger.Info("Module error stats",
				zap.String("module", moduleName),
				zap.Int64("error_count", stats.ErrorCount),
				zap.Time("last_error", stats.LastError),
			)
		}
	}

	// 如果磁盘使用率过高，输出警告
	if logRotationData.DiskUsagePercent > 80 {
		mm.logger.Warn("High disk usage detected",
			zap.Float64("usage_percent", logRotationData.DiskUsagePercent),
			zap.Int64("available_mb", logRotationData.DiskAvailableMB),
		)
	}

	// 如果日志轮转不健康，输出警告
	if !logRotationData.IsRotationHealthy {
		mm.logger.Warn("Log rotation unhealthy",
			zap.Int64("current_size_mb", logRotationData.CurrentLogSizeMB),
			zap.Int("backup_count", logRotationData.BackupCount),
		)
	}
}
