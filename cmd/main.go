package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"rtb_ms_exporter_go/conf"
	"rtb_ms_exporter_go/internal/models"
	"rtb_ms_exporter_go/internal/scheduler"
	"rtb_ms_exporter_go/internal/storage"
	"rtb_ms_exporter_go/internal/watcher"
	"rtb_ms_exporter_go/internal/zaplog"
	"strings"
	"syscall"
	"time"

	"go.uber.org/zap"
)

var __version__ = "__BUILD_VERSION__"

func main() {
	configFile := flag.String("c", "conf/rtb_ms_exporter.yaml", "default conf/rtb_ms_exporter.yaml")
	pv := flag.Bool("v", false, "print version")
	debugOSS := flag.Bool("debug-oss", false, "enable OSS upload debug mode (only print upload actions, do not actually upload)")
	updateOnStartup := flag.Bool("update-on-startup", false, "execute budget/device/resource/adindex tasks once on startup")
	flag.Parse()

	log.Println("Using config file: " + *configFile)
	err := conf.ConfigInit(*configFile)
	if err != nil {
		log.Fatal("config init error", err)
	}

	// 设置OSS调试模式
	conf.GlobalConfig.DebugOSS = *debugOSS
	if *debugOSS {
		log.Println("OSS debug mode enabled - will only print upload actions without actual uploading")
	}

	log.Println("Current version: " + __version__)
	if *pv {
		log.Println("current run version:" + __version__)
		os.Exit(0)
	}

	// 初始化日志
	zaplog.InitLogger(getLogOption())
	logger := zaplog.Logger

	// 启动应用
	if err := runApplication(logger, *updateOnStartup); err != nil {
		log.Printf("Application failed to start: %v", err)
		logger.Fatal("Application failed to start", zap.Error(err))
	}
	time.Sleep(time.Second)
}

func runApplication(logger *zap.Logger, updateOnStartup bool) error {
	config := conf.GlobalConfig

	// 初始化数据库管理器
	dbManager := models.NewDatabaseManager(logger)
	if err := dbManager.InitDatabases(config.Databases); err != nil {
		return err
	}
	defer dbManager.Close()

	// 初始化存储管理器
	storageManager := storage.NewStorageManager(logger)
	storageManager.SetDebugMode(config.DebugOSS)
	if err := storageManager.InitProviders(config.OSSProviders); err != nil {
		return err
	}
	defer storageManager.Close()

	// 初始化本地文件管理器
	localManager := watcher.NewLocalFileManager(config.LocalStorage.BaseDir, logger)

	// 初始化AdIndex文件监听器
	adIndexWatcher, err := watcher.NewAdIndexWatcher(
		config.AdIndex.WatchDir,
		config.AdIndex.FilePattern,
		config.AdIndex.KeepFiles,
		storageManager,
		logger,
	)
	if err != nil {
		return err
	}

	// 启动AdIndex监听器
	if err := adIndexWatcher.Start(); err != nil {
		return err
	}
	defer adIndexWatcher.Stop()

	// 初始化任务调度器
	taskScheduler := scheduler.NewTaskScheduler(
		dbManager,
		storageManager,
		localManager,
		config.Schedule,
		logger,
	)

	// 启动任务调度器
	if err := taskScheduler.Start(); err != nil {
		return err
	}
	defer taskScheduler.Stop()

	// 如果设置了启动时更新参数，立即执行所有任务
	if updateOnStartup {
		logger.Info("Update on startup enabled, executing all tasks once")
		if err := taskScheduler.ExecuteAllTasksOnce(); err != nil {
			logger.Error("Failed to execute tasks on startup", zap.Error(err))
			return fmt.Errorf("failed to execute tasks on startup: %v", err)
		}
	}

	// 初始化清理管理器
	cleanupManager := scheduler.NewCleanupManager(
		storageManager,
		localManager,
		logger,
	)

	// 启动清理调度
	cleanupManager.StartCleanupSchedule(
		1, // OSS文件清理1天
		config.LocalStorage.CleanupDays,
	)

	logger.Info("RTB MS Exporter started successfully")

	// 等待信号
	waitForSignal(logger)

	return nil
}

func waitForSignal(logger *zap.Logger) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	sig := <-sigChan
	logger.Info("Received signal, shutting down", zap.String("signal", sig.String()))
}

func getLogOption() zaplog.Option {
	config := conf.GlobalConfig.LogConfig
	hostName, _ := os.Hostname()
	return zaplog.Option{
		ServiceName: string(conf.GlobalConfig.Server.Name),
		LogPath:     strings.Replace(config.LogPath, "__POD__", hostName, -1),
		LogLevel:    config.LogLevel,
		MaxSize:     config.MaxSize,
		MaxAgeDays:  config.MaxAgeDays,
		MaxBackups:  config.MaxBackups,
		Compress:    config.Compress,
	}
}
